import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../models/azkar_completion_model.dart';
import '../models/azkar_model.dart';
import '../services/azkar_provider.dart';
import '../widgets/zikr_list_item.dart';
import '../widgets/shimmer_loading.dart';
import '../widgets/enhanced_animations.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/azkar_settings_bottom_sheet.dart';
import '../widgets/islamic_background.dart';
import '../services/azkar_settings_service.dart';
import '../services/sound_service.dart';

/// شاشة عرض قائمة الأذكار لتصنيف معين
/// تدعم العرض، التفاعل، الإعدادات، والإحصائيات
class AzkarListScreen extends StatefulWidget {
  final String category;

  const AzkarListScreen({super.key, required this.category});

  @override
  State<AzkarListScreen> createState() => _AzkarListScreenState();
}

class _AzkarListScreenState extends State<AzkarListScreen>
    with TickerProviderStateMixin {
  // متغيرات التحكم في الأنيميشن
  int? _recentlyCompletedZikrId;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  // متغيرات إدارة الحالة
  StreamSubscription<AzkarCompletionModel>? _completionSubscription;

  // إدارة حالة الإعدادات والفلترة
  bool _hideCompletedAzkar = false;
  final Set<int> _hidingZikrIds = <int>{};

  // متغيرات إدارة الأنيميشن للإخفاء
  final Map<int, AnimationController> _hideAnimationControllers = {};
  final Map<int, Animation<double>> _hideAnimations = {};

  @override
  void initState() {
    super.initState();
    try {
      _initializeAnimations();
      _loadSettings();
      _loadAzkar();
      _setupCompletionListener();
    } catch (e) {
      debugPrint('خطأ في تهيئة شاشة الأذكار: $e');
    }
  }

  /// تحميل إعدادات الإخفاء
  Future<void> _loadSettings() async {
    try {
      final hideCompleted = await AzkarSettingsService.getHideCompletedAzkar();
      if (mounted) {
        setState(() {
          _hideCompletedAzkar = hideCompleted;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل الإعدادات: $e');
    }
  }

  /// تصفية الأذكار حسب إعدادات الإخفاء
  List<Zikr> _getFilteredAzkar(List<Zikr> azkar) {
    if (!_hideCompletedAzkar) {
      return azkar;
    }
    return azkar.where((zikr) => zikr.currentCount < zikr.count).toList();
  }

  /// إنشاء أنيميشن إخفاء للذكر المكتمل
  void _createHideAnimation(int zikrId) {
    if (_hideAnimationControllers.containsKey(zikrId)) return;

    final controller = AnimationController(
      duration: const Duration(
        milliseconds: 450,
      ), // زيادة المدة قليلاً لتأثير أكثر سلاسة
      vsync: this,
    );

    // أنيميشن مركب: تلاشي + انزلاق + تقليص
    final animation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: controller,
        curve: Curves.elasticIn, // منحنى أكثر جمالية مع تأثير فقاعة
      ),
    );

    _hideAnimationControllers[zikrId] = controller;
    _hideAnimations[zikrId] = animation;
  }

  /// إخفاء الذكر مع أنيميشن سلس
  Future<void> _hideZikrWithAnimation(int zikrId) async {
    if (!_hideCompletedAzkar || _hidingZikrIds.contains(zikrId)) return;

    _hidingZikrIds.add(zikrId);
    _createHideAnimation(zikrId);

    final controller = _hideAnimationControllers[zikrId];
    if (controller != null) {
      // تشغيل صوت الفقاعة في بداية الإخفاء
      await SoundService.playBubblePopSound();

      // تشغيل الأنيميشن مع تأثير محسن
      await controller.forward();

      // انتظار إضافي للتأثير البصري المحسن
      await Future.delayed(const Duration(milliseconds: 150));

      if (mounted) {
        setState(() {
          // إعادة بناء القائمة لإزالة العنصر المكتمل
          // سيتم تطبيق الفلترة الآن بعد انتهاء الأنيميشن
        });
      }
    }

    _hidingZikrIds.remove(zikrId);
  }

  /// تهيئة الأنيميشن والتأثيرات
  void _initializeAnimations() {
    // إعداد حركة انزلاق الذكر المكتمل
    _slideController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(1.2, 0),
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.elasticOut),
    );

    // إعداد أنيميشن النبض المحسن للتأثيرات البصرية
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.15).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.elasticOut),
    );
  }

  /// إعداد مستمع إكمال الأذكار
  void _setupCompletionListener() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      try {
        final azkarProvider = Provider.of<AzkarProvider>(
          context,
          listen: false,
        );
        _completionSubscription = azkarProvider.onAzkarCategoryCompleted.listen(
          _handleAzkarCategoryCompleted,
          onError: (error) {
            debugPrint('خطأ في مستمع إكمال الأذكار: $error');
          },
        );
      } catch (e) {
        debugPrint('خطأ في إعداد مستمع إكمال الأذكار: $e');
      }
    });
  }

  @override
  void dispose() {
    try {
      _slideController.dispose();
      _pulseController.dispose();
      _completionSubscription?.cancel();

      // تنظيف أنيميشن الإخفاء
      for (final controller in _hideAnimationControllers.values) {
        controller.dispose();
      }
      _hideAnimationControllers.clear();
      _hideAnimations.clear();
    } catch (e) {
      debugPrint('خطأ في تنظيف الموارد: $e');
    }
    super.dispose();
  }

  /// تحميل الأذكار للتصنيف المحدد
  void _loadAzkar() {
    if (!mounted) return;

    try {
      Provider.of<AzkarProvider>(
        context,
        listen: false,
      ).loadAzkarByCategory(widget.category);
    } catch (e) {
      debugPrint('خطأ في تحميل الأذكار: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تحميل الأذكار'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// معالجة حدث إكمال الأذكار
  void _handleAzkarCategoryCompleted(AzkarCompletionModel completionModel) {
    if (!mounted) return;

    try {
      // التحقق من أن الصنف المكتمل هو الصنف الحالي
      if (completionModel.category == widget.category) {
        // تشغيل تأثير النبض
        _pulseController.forward().then((_) {
          if (mounted) {
            _pulseController.reverse();
          }
        });

        // عرض واجهة إكمال الأذكار
        if (mounted) {
          setState(() {
            // تم إكمال التصنيف
          });
        }
      }
    } catch (e) {
      debugPrint('خطأ في معالجة إكمال الأذكار: $e');
    }
  }

  /// معالجة زيادة عداد الذكر مع تحسينات الأداء والتأثيرات
  Future<void> _handleCounterIncrement(Zikr zikr, int count) async {
    if (!mounted) return;

    // حفظ المراجع قبل العمليات غير المتزامنة
    final currentContext = context;
    final scaffoldMessenger = ScaffoldMessenger.of(currentContext);
    final provider = Provider.of<AzkarProvider>(currentContext, listen: false);

    try {
      // تنفيذ الاهتزاز إذا كان مفعلاً
      await AzkarSettingsService.performVibration();

      // التحقق مما إذا كان الذكر قد اكتمل
      final wasCompleted =
          zikr.currentCount < zikr.count && count >= zikr.count;

      // تحديث عداد الذكر
      await provider.updateZikrCount(
        zikr,
        count,
        mounted ? currentContext : null,
      );

      // إذا كان الذكر قد اكتمل، قم بتشغيل التأثيرات
      if (wasCompleted && mounted) {
        // تشغيل صوت النجاح عند إكمال الذكر
        await SoundService.playSuccessSound();

        // تشغيل أنيميشن الإخفاء إذا كان مفعلاً
        if (_hideCompletedAzkar) {
          await _hideZikrWithAnimation(zikr.id);
        }

        if (mounted && currentContext.mounted) {
          await _handleZikrCompletion(zikr, scaffoldMessenger, currentContext);
        }
      }
    } catch (e) {
      debugPrint('خطأ في معالجة زيادة العداد: $e');
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في تحديث العداد'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// معالجة إكمال الذكر مع التأثيرات البصرية
  Future<void> _handleZikrCompletion(
    Zikr zikr,
    ScaffoldMessengerState scaffoldMessenger,
    BuildContext currentContext,
  ) async {
    if (!mounted) return;

    // حفظ الألوان قبل العمليات غير المتزامنة
    final primaryColor = Theme.of(currentContext).colorScheme.primary;

    try {
      // اهتزاز أقوى عند الإكمال
      await AzkarSettingsService.performMediumVibration();

      // عرض رسالة "أحسنت" محسنة
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 10),
              const Text(
                'أحسنت! تم إكمال الذكر',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          backgroundColor: primaryColor,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );

      // تشغيل تأثير النبض
      _pulseController.forward().then((_) {
        if (mounted) {
          _pulseController.reverse();
        }
      });

      // تحديث حالة الذكر المكتمل
      if (mounted) {
        setState(() {
          _recentlyCompletedZikrId = zikr.id;
        });
      }

      // تشغيل حركة الانزلاق
      await _slideController.forward();

      if (mounted) {
        // إعادة ضبط حركة الانزلاق بعد الانتهاء
        _slideController.reset();
        setState(() {
          _recentlyCompletedZikrId = null;
        });

        // التحقق من إكمال جميع الأذكار في التصنيف
        _checkCategoryCompletion();
      }
    } catch (e) {
      debugPrint('خطأ في معالجة إكمال الذكر: $e');
    }
  }

  // التحقق من إكمال جميع الأذكار في التصنيف
  void _checkCategoryCompletion() {
    final provider = Provider.of<AzkarProvider>(context, listen: false);
    final allCompleted = provider.currentAzkar.every(
      (zikr) => zikr.currentCount >= zikr.count,
    );

    if (allCompleted && provider.currentAzkar.isNotEmpty) {
      _showCategoryCompletionDialog();
    }
  }

  // عرض رسالة التهنئة عند إكمال جميع الأذكار
  void _showCategoryCompletionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            elevation: 16,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    Theme.of(context).cardColor,
                  ],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // أيقونة النجمة الذهبية
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.amber,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withValues(alpha: 0.3),
                          blurRadius: 12,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.star,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // رسالة التهنئة
                  Text(
                    'بارك الله فيك!',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),

                  Text(
                    'لقد أكملت جميع الأذكار في "${widget.category}"',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),

                  // رسالة إضافية
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.amber.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'تقبل الله منك صالح الأعمال',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.amber.shade700,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 24),

                  // زر الإغلاق
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 12,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'الحمد لله',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );

    // تأثير اهتزاز للإكمال
    HapticFeedback.heavyImpact();
  }

  /// بناء عنصر الذكر مع التأثيرات والتفاعلات
  Widget _buildZikrItem(Zikr zikr, AzkarProvider provider) {
    // التحقق من وجود أنيميشن إخفاء لهذا الذكر
    final hideAnimation = _hideAnimations[zikr.id];

    Widget zikrWidget = ScaleTransition(
      scale:
          _recentlyCompletedZikrId == zikr.id
              ? _pulseAnimation
              : const AlwaysStoppedAnimation(1.0),
      child: ZikrListItem(
        zikr: zikr,
        onTap: () {
          // لا نقوم بالانتقال إلى صفحة التفاصيل، بل نترك التوسيع/الطي للعنصر نفسه
        },
        onFavoriteToggle: () async {
          try {
            // إضافة تأثير حركي عند النقر على المفضلة
            HapticFeedback.lightImpact();
            await provider.toggleFavorite(zikr);
          } catch (e) {
            debugPrint('خطأ في تبديل المفضلة: $e');
          }
        },
        onCounterIncrement:
            (zikr, count) => _handleCounterIncrement(zikr, count),
        showEditButton: false, // إخفاء زر التعديل
      ),
    );

    // إضافة أنيميشن الإخفاء إذا كان متوفراً
    if (hideAnimation != null) {
      zikrWidget = AnimatedBuilder(
        animation: hideAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: hideAnimation.value,
            child: Transform.translate(
              offset: Offset(
                (1.0 - hideAnimation.value) * -200, // انزلاق إلى اليسار
                0,
              ),
              child: Opacity(opacity: hideAnimation.value, child: child),
            ),
          );
        },
        child: zikrWidget,
      );
    }

    return zikrWidget;
  }

  /// عرض مربع حوار إضافة ذكر جديد مع تحسينات الأمان
  void _showAddZikrDialog(BuildContext context) {
    if (!mounted) return;

    try {
      // الحصول على مزود الأذكار
      final provider = Provider.of<AzkarProvider>(context, listen: false);

      // متغيرات لحفظ بيانات الذكر الجديد
      final TextEditingController textController = TextEditingController();
      final TextEditingController countController = TextEditingController(
        text: '1',
      );
      final TextEditingController fadlController = TextEditingController();

      showDialog(
        context: context,
        builder: (BuildContext dialogContext) {
          return AlertDialog(
            title: const Text('إضافة ذكر جديد', textAlign: TextAlign.center),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // حقل نص الذكر
                  TextField(
                    controller: textController,
                    decoration: const InputDecoration(
                      labelText: 'نص الذكر',
                      hintText: 'أدخل نص الذكر',
                    ),
                    maxLines: 3,
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 16),

                  // حقل عدد التكرار
                  TextField(
                    controller: countController,
                    decoration: const InputDecoration(
                      labelText: 'عدد التكرار',
                      hintText: 'أدخل عدد التكرار',
                    ),
                    keyboardType: TextInputType.number,
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.right,
                  ),
                  const SizedBox(height: 16),

                  // حقل فضل الذكر (اختياري)
                  TextField(
                    controller: fadlController,
                    decoration: const InputDecoration(
                      labelText: 'فضل الذكر (اختياري)',
                      hintText: 'أدخل فضل الذكر',
                    ),
                    maxLines: 2,
                    textDirection: TextDirection.rtl,
                    textAlign: TextAlign.right,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(dialogContext),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // التحقق من صحة البيانات
                  if (textController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('يرجى إدخال نص الذكر')),
                    );
                    return;
                  }

                  // التحقق من صحة عدد التكرار
                  int? count = int.tryParse(countController.text);
                  if (count == null || count <= 0) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('يرجى إدخال عدد تكرار صحيح'),
                      ),
                    );
                    return;
                  }

                  // إنشاء كائن الذكر الجديد
                  final newZikr = Zikr(
                    id: 0, // سيتم تعيينه تلقائ
                    text: textController.text,
                    count: count,
                    source: widget.category, // استخدام التصنيف الحالي
                    category: widget.category,
                    isFavorite: false,
                    currentCount: 0,
                    isCustom: true, // تعيين علامة أنه ذكر مخصص
                    fadl:
                        fadlController.text.isNotEmpty
                            ? fadlController.text
                            : null,
                  );

                  // إضافة الذكر الجديد
                  provider.addCustomZikr(newZikr);

                  // إغلاق مربع الحوار
                  Navigator.pop(dialogContext);

                  // عرض رسالة نجاح
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تمت إضافة الذكر بنجاح')),
                  );
                },
                child: const Text('إضافة'),
              ),
            ],
          );
        },
      );
    } catch (e) {
      debugPrint('خطأ في عرض حوار إضافة الذكر: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('حدث خطأ في عرض النافذة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// عرض قائمة إعدادات الأذكار
  void _showAzkarSettings() {
    if (!mounted) return;

    try {
      showAzkarSettingsBottomSheet(
        context: context,
        onSettingsChanged: () {
          // إعادة تحميل الإعدادات وإعادة بناء الواجهة عند تغيير الإعدادات
          _loadSettings();
        },
      );
    } catch (e) {
      debugPrint('خطأ في عرض إعدادات الأذكار: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // الحصول على السمة الحالية
    final theme = Theme.of(context);

    return Scaffold(
      // استخدام لون الخلفية من السمة
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: CustomAppBar(
        title: widget.category,
        actions: [
          // زر إضافة ذكر جديد
          IconButton(
            icon: Icon(
              Icons.edit_note,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'إضافة ذكر',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              _showAddZikrDialog(context);
            },
          ),
          // زر الإعدادات
          IconButton(
            icon: Icon(
              Icons.settings,
              color: theme.colorScheme.onSurface,
              size: 24,
            ),
            tooltip: 'إعدادات الأذكار',
            onPressed: () {
              HapticFeedback.lightImpact(); // تأثير اهتزاز خفيف
              _showAzkarSettings();
            },
          ),
        ],
      ),
      body: IslamicBackground(
        opacity: theme.brightness == Brightness.dark ? 0.02 : 0.03,
        showPattern: true,
        showGradient: false,
        child: Stack(
          children: [
            Consumer<AzkarProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return ShimmerList(
                    itemCount: 5,
                    itemHeight: 120,
                    showAvatar: false,
                  );
                }

                if (provider.currentAzkar.isEmpty) {
                  return const Center(
                    child: Text('لا توجد أذكار في هذا التصنيف'),
                  );
                }

                // تطبيق الفلترة المحلية بدلاً من FutureBuilder لتجنب إعادة التحميل المستمر
                final filteredAzkar = _getFilteredAzkar(provider.currentAzkar);

                if (filteredAzkar.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.menu_book_outlined,
                          size: 64,
                          color: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.5),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _hideCompletedAzkar &&
                                  provider.currentAzkar.isNotEmpty
                              ? 'تم إكمال جميع الأذكار في هذا التصنيف'
                              : 'لا توجد أذكار في هذا التصنيف',
                          style: Theme.of(context).textTheme.titleMedium,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _hideCompletedAzkar &&
                                  provider.currentAzkar.isNotEmpty
                              ? 'بارك الله فيك! يمكنك إعادة ضبط العدادات أو إضافة أذكار جديدة'
                              : 'يمكنك إضافة أذكار جديدة باستخدام زر الإضافة',
                          style: Theme.of(
                            context,
                          ).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(
                              context,
                            ).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                }

                // ListView محسن جذري للأجهزة الضعيفة
                return ListView.builder(
                  padding: const EdgeInsets.all(8.0), // تقليل الحشو
                  // تحسينات جذرية للأداء
                  cacheExtent: 150, // تقليل جذري للتخزين المؤقت
                  physics: const ClampingScrollPhysics(), // فيزياء محسنة
                  itemCount:
                      filteredAzkar.length > 15
                          ? 15
                          : filteredAzkar.length, // حد أقصى 15
                  itemBuilder: (context, index) {
                    final zikr = filteredAzkar[index];

                    // التحقق مما إذا كان هذا الذكر هو الذي تم إكماله مؤخراً
                    final isRecentlyCompleted =
                        _recentlyCompletedZikrId == zikr.id;

                    // استخدام FadeInAnimation لإضافة تأثير حركي عند ظهور العناصر
                    return FadeInAnimation(
                      delay: Duration(milliseconds: (index * 50).clamp(0, 500)),
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 12.0),
                        child: RepaintBoundary(
                          child:
                              isRecentlyCompleted
                                  ? SlideTransition(
                                    position: _slideAnimation,
                                    child: _buildZikrItem(zikr, provider),
                                  )
                                  : _buildZikrItem(zikr, provider),
                        ),
                      ),
                    );
                  },
                  // تحسينات إضافية للأداء
                  addAutomaticKeepAlives: false,
                  addRepaintBoundaries: false, // نحن نضيفها يدو
                  addSemanticIndexes: false,
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

